#!/usr/bin/env python3
"""
Complete Data Validation Module

This single script performs all data validation actions:
1. Combines SCADA data from Excel files
2. Loads API data from CSV
3. Compares both sources by datetime
4. Performs detailed analysis
5. Generates comprehensive reports
6. Creates visualizations

Usage: python complete_data_validation.py
"""

import pandas as pd
import numpy as np
import os
import re
import glob
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Try to import matplotlib for visualizations
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    print("Note: matplotlib/seaborn not available. Visualizations will be skipped.")

class DataValidationSystem:
    """Complete data validation system for API vs SCADA comparison."""
    
    def __init__(self, scada_folders=None, api_file=None, output_dir="validation_results"):
        """
        Initialize the validation system.
        
        Args:
            scada_folders (list): List of folders containing SCADA Excel files
            api_file (str): Path to API CSV file
            output_dir (str): Directory to save results
        """
        self.scada_folders = scada_folders or ['Feb-25', 'Mar-25']
        self.api_file = api_file or 'IN.INTE.KIDS_Generation - IN.INTE.KIDS_Generation.csv'
        self.output_dir = output_dir
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Data storage
        self.scada_data = None
        self.api_data = None
        self.comparison_results = None
        self.analysis_results = {}
        
        print("=" * 60)
        print("🔍 COMPLETE DATA VALIDATION SYSTEM")
        print("=" * 60)
        print(f"📁 SCADA Folders: {self.scada_folders}")
        print(f"📄 API File: {self.api_file}")
        print(f"💾 Output Directory: {self.output_dir}")
        print("=" * 60)
    
    def extract_inverter_name(self, filename):
        """Extract inverter name from filename."""
        match = re.search(r'INV_\d+', filename)
        if match:
            return match.group()
        else:
            match = re.search(r'Inverter___(.+?)\(', filename)
            if match:
                return match.group(1)
            else:
                return Path(filename).stem
    
    def process_excel_file(self, file_path):
        """Process a single Excel file and extract required data."""
        try:
            df = pd.read_excel(file_path)
            
            required_columns = ['Date & Time', 'Interval Gen (KWh)']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"⚠️  Warning: Missing columns {missing_columns} in {file_path}")
                return None
            
            result_df = df[required_columns].copy()
            result_df = result_df.dropna(subset=['Date & Time'])
            
            # Filter valid date entries
            date_pattern = r'\d{2}/\d{2}/\d{4}'
            result_df = result_df[result_df['Date & Time'].astype(str).str.contains(date_pattern, na=False)]
            
            # Extract inverter name
            filename = os.path.basename(file_path)
            inverter_name = self.extract_inverter_name(filename)
            result_df['Inverter Name'] = inverter_name
            
            # Convert data types
            try:
                result_df['Date & Time'] = pd.to_datetime(result_df['Date & Time'], format='%d/%m/%Y %H:%M:%S')
            except:
                try:
                    result_df['Date & Time'] = pd.to_datetime(result_df['Date & Time'])
                except:
                    print(f"⚠️  Warning: Could not parse dates in {file_path}")
            
            result_df['Interval Gen (KWh)'] = pd.to_numeric(result_df['Interval Gen (KWh)'], errors='coerce')
            result_df = result_df.dropna(subset=['Interval Gen (KWh)'])
            
            return result_df
        except Exception as e:
            print(f"❌ Error processing {file_path}: {str(e)}")
            return None
    
    def load_scada_data(self):
        """Load and combine SCADA data from Excel files."""
        print("\n📊 STEP 1: Loading SCADA Data")
        print("-" * 40)
        
        excel_files = []
        for folder in self.scada_folders:
            if os.path.exists(folder):
                pattern = os.path.join(folder, '**', '*.xlsx')
                files = glob.glob(pattern, recursive=True)
                excel_files.extend(files)
                print(f"📁 Found {len(files)} Excel files in {folder}")
            else:
                print(f"⚠️  Warning: Folder {folder} does not exist")
        
        if not excel_files:
            raise Exception("❌ No Excel files found!")
        
        print(f"📄 Total Excel files found: {len(excel_files)}")
        
        # Process each file
        all_data = []
        for file_path in excel_files:
            processed_data = self.process_excel_file(file_path)
            if processed_data is not None and not processed_data.empty:
                all_data.append(processed_data)
        
        if not all_data:
            raise Exception("❌ No valid data found in Excel files!")
        
        # Combine and pivot data
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_df = combined_df.sort_values(['Date & Time', 'Inverter Name']).reset_index(drop=True)
        
        # Pivot to have inverter names as columns
        pivoted_df = combined_df.pivot_table(
            index='Date & Time',
            columns='Inverter Name',
            values='Interval Gen (KWh)',
            aggfunc='first'
        ).reset_index()
        
        # Sort columns naturally
        inverter_columns = [col for col in pivoted_df.columns if col != 'Date & Time']
        inverter_columns.sort(key=lambda x: (int(x.split('_')[1]) if '_' in x and x.split('_')[1].isdigit() else float('inf'), x))
        column_order = ['Date & Time'] + inverter_columns
        pivoted_df = pivoted_df[column_order]
        
        self.scada_data = pivoted_df
        
        # Save SCADA data
        scada_output = os.path.join(self.output_dir, 'scada_combined_data.csv')
        self.scada_data.to_csv(scada_output, index=False)
        
        print(f"✅ SCADA data loaded: {len(self.scada_data)} records, {len(inverter_columns)} inverters")
        print(f"📅 Date range: {self.scada_data['Date & Time'].min()} to {self.scada_data['Date & Time'].max()}")
        print(f"🔧 Inverters: {inverter_columns}")
        print(f"💾 Saved to: {scada_output}")
    
    def load_api_data(self):
        """Load and process API data."""
        print("\n🌐 STEP 2: Loading API Data")
        print("-" * 40)
        
        if not os.path.exists(self.api_file):
            raise Exception(f"❌ API file not found: {self.api_file}")
        
        # Read API data
        api_df = pd.read_csv(self.api_file)
        
        # Convert time and remove timezone
        api_df['time'] = pd.to_datetime(api_df['time']).dt.tz_localize(None)
        api_df = api_df.rename(columns={'time': 'Date & Time'})
        
        # Extract and standardize inverter columns
        inverter_columns = {}
        for col in api_df.columns:
            if col != 'Date & Time':
                # Handle both INV_X and INVXX.Daily Energy patterns
                if 'INV_' in col:
                    match = re.search(r'INV_(\d+)', col)
                    if match:
                        inv_num = match.group(1)
                        standard_name = f'INV_{inv_num}'
                        inverter_columns[col] = standard_name
                elif 'INV' in col and 'Daily Energy' in col:
                    match = re.search(r'INV(\d+)', col)
                    if match:
                        inv_num = int(match.group(1))
                        standard_name = f'INV_{inv_num}'
                        inverter_columns[col] = standard_name
        
        # Rename and keep relevant columns
        api_df = api_df.rename(columns=inverter_columns)
        keep_columns = ['Date & Time'] + list(inverter_columns.values())
        api_df = api_df[keep_columns]
        
        # Convert to numeric
        for col in api_df.columns:
            if col != 'Date & Time':
                api_df[col] = pd.to_numeric(api_df[col], errors='coerce')

        # Sort by timestamp
        api_df = api_df.sort_values('Date & Time')

        # Check if data appears to be cumulative (values generally increase)
        is_cumulative = self.detect_cumulative_data(api_df, list(inverter_columns.values()))

        if is_cumulative:
            print("🔄 Detected cumulative data - converting to interval values...")
            api_df = self.convert_cumulative_to_interval(api_df, list(inverter_columns.values()))
            print("✅ Conversion completed")

        self.api_data = api_df

        # Save API data
        api_output = os.path.join(self.output_dir, 'api_processed_data.csv')
        self.api_data.to_csv(api_output, index=False)

        print(f"✅ API data loaded: {len(self.api_data)} records, {len(inverter_columns)} inverters")
        print(f"📅 Date range: {self.api_data['Date & Time'].min()} to {self.api_data['Date & Time'].max()}")
        print(f"🔧 Inverters: {sorted(list(inverter_columns.values()))}")
        print(f"💾 Saved to: {api_output}")

    def detect_cumulative_data(self, df, inverter_columns):
        """Detect if the data appears to be cumulative (generally increasing values)."""
        if len(df) < 10:  # Need enough data points
            return False

        cumulative_indicators = 0
        total_checks = 0

        for col in inverter_columns:
            if col in df.columns:
                # Get non-null values
                values = df[col].dropna()
                if len(values) < 5:
                    continue

                # Check if values generally increase (allowing for some resets at day boundaries)
                increasing_count = 0
                total_transitions = 0

                for i in range(1, len(values)):
                    if values.iloc[i] >= values.iloc[i-1]:
                        increasing_count += 1
                    total_transitions += 1

                # If more than 70% of transitions are increasing, consider it cumulative
                if total_transitions > 0:
                    increasing_ratio = increasing_count / total_transitions
                    if increasing_ratio > 0.7:
                        cumulative_indicators += 1
                    total_checks += 1

        # If more than half of the inverters show cumulative behavior
        return total_checks > 0 and (cumulative_indicators / total_checks) > 0.5

    def convert_cumulative_to_interval(self, df, inverter_columns):
        """Convert cumulative daily energy to interval generation values."""
        df_converted = df.copy()

        # Group by date to handle daily resets
        df_converted['Date'] = df_converted['Date & Time'].dt.date

        for col in inverter_columns:
            if col in df_converted.columns:
                interval_values = []

                for date, group in df_converted.groupby('Date'):
                    group = group.sort_values('Date & Time')

                    # First value of the day is the interval value
                    if len(group) > 0:
                        interval_values.append(group[col].iloc[0])

                    # Calculate differences for subsequent values
                    for i in range(1, len(group)):
                        prev_val = group[col].iloc[i-1]
                        curr_val = group[col].iloc[i]

                        if pd.isna(prev_val) or pd.isna(curr_val):
                            interval_values.append(np.nan)
                        else:
                            # Handle potential resets (when current < previous)
                            if curr_val < prev_val:
                                # Assume it's a reset, use current value as interval
                                interval_values.append(curr_val)
                            else:
                                # Normal case: difference
                                interval_values.append(curr_val - prev_val)

                # Replace the column with interval values
                df_converted[col] = interval_values

        # Remove the temporary Date column
        df_converted = df_converted.drop('Date', axis=1)

        return df_converted
    
    def align_and_compare_data(self, tolerance_minutes=15, tolerance_percent=5.0):
        """Align timestamps and compare data between API and SCADA."""
        print(f"\n🔄 STEP 3: Aligning and Comparing Data")
        print("-" * 40)
        
        # Create working copies
        api_work = self.api_data.copy()
        scada_work = self.scada_data.copy()
        
        # Round timestamps
        api_work['Date & Time'] = api_work['Date & Time'].dt.round(f'{tolerance_minutes}min')
        scada_work['Date & Time'] = scada_work['Date & Time'].dt.round(f'{tolerance_minutes}min')
        
        # Find overlap period
        api_start, api_end = api_work['Date & Time'].min(), api_work['Date & Time'].max()
        scada_start, scada_end = scada_work['Date & Time'].min(), scada_work['Date & Time'].max()
        overlap_start, overlap_end = max(api_start, scada_start), min(api_end, scada_end)
        
        print(f"📅 API range: {api_start} to {api_end}")
        print(f"📅 SCADA range: {scada_start} to {scada_end}")
        print(f"📅 Overlap range: {overlap_start} to {overlap_end}")
        
        # Filter to overlap period
        api_overlap = api_work[(api_work['Date & Time'] >= overlap_start) & 
                              (api_work['Date & Time'] <= overlap_end)].copy()
        scada_overlap = scada_work[(scada_work['Date & Time'] >= overlap_start) & 
                                  (scada_work['Date & Time'] <= overlap_end)].copy()
        
        # Find common timestamps
        common_times = set(api_overlap['Date & Time']).intersection(set(scada_overlap['Date & Time']))
        print(f"🕐 Common timestamps: {len(common_times)}")
        
        # Filter to common timestamps
        aligned_api = api_overlap[api_overlap['Date & Time'].isin(common_times)].sort_values('Date & Time').reset_index(drop=True)
        aligned_scada = scada_overlap[scada_overlap['Date & Time'].isin(common_times)].sort_values('Date & Time').reset_index(drop=True)
        
        # Merge and compare
        merged = pd.merge(aligned_api, aligned_scada, on='Date & Time', suffixes=('_API', '_SCADA'))
        
        # Find common inverters
        api_inverters = [col.replace('_API', '') for col in merged.columns if col.endswith('_API')]
        scada_inverters = [col.replace('_SCADA', '') for col in merged.columns if col.endswith('_SCADA')]
        common_inverters = sorted(set(api_inverters).intersection(set(scada_inverters)))
        
        print(f"🔧 Common inverters: {common_inverters}")

        # Check if we have common inverters
        if not common_inverters:
            print("❌ No common inverters found between API and SCADA data!")
            print("🔍 Checking API data columns...")
            api_cols = [col for col in self.api_data.columns if col != 'Date & Time']
            print(f"📊 API columns: {api_cols}")
            print("🔍 Checking SCADA data columns...")
            scada_cols = [col for col in self.scada_data.columns if col != 'Date & Time']
            print(f"📊 SCADA columns: {scada_cols}")

            # Create empty comparison results with proper structure
            self.comparison_results = pd.DataFrame(columns=[
                'Date & Time', 'Inverter', 'API_Value', 'SCADA_Value',
                'Difference', 'Percent_Difference', 'Match_Status'
            ])
        else:
            # Perform comparison
            comparison_results = []
            for _, row in merged.iterrows():
                timestamp = row['Date & Time']

                for inverter in common_inverters:
                    api_col = f'{inverter}_API'
                    scada_col = f'{inverter}_SCADA'

                    api_value = row[api_col]
                    scada_value = row[scada_col]

                    # Calculate differences
                    if pd.isna(api_value) or pd.isna(scada_value):
                        match_status = 'Missing Data'
                        difference = np.nan
                        percent_diff = np.nan
                    else:
                        difference = abs(api_value - scada_value)
                        if scada_value != 0:
                            percent_diff = (difference / abs(scada_value)) * 100
                        else:
                            percent_diff = 0 if api_value == 0 else 100

                        match_status = 'Match' if percent_diff <= tolerance_percent else 'Mismatch'

                    comparison_results.append({
                        'Date & Time': timestamp,
                        'Inverter': inverter,
                        'API_Value': api_value,
                        'SCADA_Value': scada_value,
                        'Difference': difference,
                        'Percent_Difference': percent_diff,
                        'Match_Status': match_status
                    })

            self.comparison_results = pd.DataFrame(comparison_results)
        
        # Save comparison results
        comparison_output = os.path.join(self.output_dir, 'comparison_results.csv')
        self.comparison_results.to_csv(comparison_output, index=False)

        # Print summary
        total = len(self.comparison_results)
        if total > 0:
            matches = len(self.comparison_results[self.comparison_results['Match_Status'] == 'Match'])
            mismatches = len(self.comparison_results[self.comparison_results['Match_Status'] == 'Mismatch'])
            missing = len(self.comparison_results[self.comparison_results['Match_Status'] == 'Missing Data'])

            print(f"✅ Comparison completed: {total:,} total comparisons")
            print(f"✅ Matches: {matches:,} ({matches/total*100:.1f}%)")
            print(f"❌ Mismatches: {mismatches:,} ({mismatches/total*100:.1f}%)")
            print(f"⚠️  Missing: {missing:,} ({missing/total*100:.1f}%)")
        else:
            print("❌ No comparisons could be performed - no common inverters found!")
            print("💡 This usually means:")
            print("   - API and SCADA data have different inverter naming conventions")
            print("   - API data doesn't contain individual inverter columns")
            print("   - Column names need to be standardized")

        print(f"💾 Saved to: {comparison_output}")
    
    def perform_detailed_analysis(self):
        """Perform detailed analysis of comparison results."""
        print(f"\n📈 STEP 4: Detailed Analysis")
        print("-" * 40)

        df = self.comparison_results.copy()
        total = len(df)

        if total == 0:
            print("❌ No data available for analysis - skipping detailed analysis")
            # Create minimal analysis results for empty data
            self.analysis_results = {
                'total_comparisons': 0,
                'match_rate': 0,
                'match_counts': pd.Series(dtype=int),
                'inverter_stats': pd.DataFrame(),
                'hourly_stats': pd.DataFrame(),
                'api_higher': 0,
                'scada_higher': 0,
                'total_mismatches': 0
            }
            return

        df['Date & Time'] = pd.to_datetime(df['Date & Time'])
        df['Hour'] = df['Date & Time'].dt.hour

        # Overall statistics
        match_counts = df['Match_Status'].value_counts()
        match_rate = (match_counts.get('Match', 0) / total) * 100
        
        # Analysis by inverter
        inverter_stats = df.groupby('Inverter').agg({
            'Match_Status': lambda x: (x == 'Match').sum(),
            'Percent_Difference': ['mean', 'max', 'std'],
            'Difference': ['mean', 'max']
        }).round(2)
        
        inverter_stats.columns = ['Matches', 'Avg_Pct_Diff', 'Max_Pct_Diff', 'Std_Pct_Diff', 'Avg_Diff', 'Max_Diff']
        total_per_inverter = df.groupby('Inverter').size()
        inverter_stats['Total'] = total_per_inverter
        inverter_stats['Match_Rate'] = (inverter_stats['Matches'] / inverter_stats['Total'] * 100).round(1)
        
        # Analysis by hour
        hourly_stats = df.groupby('Hour').agg({
            'Match_Status': lambda x: (x == 'Match').sum(),
            'Percent_Difference': 'mean'
        }).round(2)
        hourly_total = df.groupby('Hour').size()
        hourly_stats['Total'] = hourly_total
        hourly_stats['Match_Rate'] = (hourly_stats['Match_Status'] / hourly_stats['Total'] * 100).round(1)
        
        # Mismatch patterns
        mismatches = df[df['Match_Status'] == 'Mismatch']
        api_higher = len(mismatches[mismatches['API_Value'] > mismatches['SCADA_Value']])
        scada_higher = len(mismatches[mismatches['SCADA_Value'] > mismatches['API_Value']])
        
        # Store analysis results
        self.analysis_results = {
            'total_comparisons': total,
            'match_rate': match_rate,
            'match_counts': match_counts,
            'inverter_stats': inverter_stats,
            'hourly_stats': hourly_stats,
            'api_higher': api_higher,
            'scada_higher': scada_higher,
            'total_mismatches': len(mismatches)
        }
        
        # Save analysis
        analysis_output = os.path.join(self.output_dir, 'analysis_results.txt')
        with open(analysis_output, 'w', encoding='utf-8') as f:
            f.write("DETAILED ANALYSIS RESULTS\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Total Comparisons: {total:,}\n")
            f.write(f"Match Rate: {match_rate:.1f}%\n\n")
            f.write("Match Status Distribution:\n")
            f.write(str(match_counts) + "\n\n")
            f.write("Analysis by Inverter:\n")
            f.write(str(inverter_stats) + "\n\n")
            f.write("Analysis by Hour:\n")
            f.write(str(hourly_stats) + "\n\n")
            f.write(f"Mismatch Patterns:\n")
            f.write(f"API Higher: {api_higher} ({api_higher/len(mismatches)*100:.1f}%)\n")
            f.write(f"SCADA Higher: {scada_higher} ({scada_higher/len(mismatches)*100:.1f}%)\n")
        
        print(f"📊 Overall match rate: {match_rate:.1f}%")
        print(f"📊 Best inverter: {inverter_stats['Match_Rate'].idxmax()} ({inverter_stats['Match_Rate'].max():.1f}%)")
        print(f"📊 Worst inverter: {inverter_stats['Match_Rate'].idxmin()} ({inverter_stats['Match_Rate'].min():.1f}%)")
        print(f"💾 Analysis saved to: {analysis_output}")
    
    def create_visualizations(self):
        """Create visualization charts."""
        if not PLOTTING_AVAILABLE:
            print("\n📊 STEP 5: Visualizations (Skipped - matplotlib not available)")
            return

        print(f"\n📊 STEP 5: Creating Visualizations")
        print("-" * 40)

        if len(self.comparison_results) == 0:
            print("❌ No data available for visualization - skipping charts")
            return

        df = self.comparison_results.copy()
        df['Hour'] = pd.to_datetime(df['Date & Time']).dt.hour
        
        # Create figure
        fig, axes = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('Detailed API vs SCADA Data Validation Analysis', fontsize=18, fontweight='bold')

        # 1. Match status pie chart with annotations
        match_counts = df['Match_Status'].value_counts()
        axes[0, 0].pie(match_counts.values, labels=match_counts.index, autopct='%1.1f%%', colors=['#4CAF50', '#FFC107', '#F44336'])
        axes[0, 0].set_title('Overall Match Status Distribution', fontsize=14, fontweight='bold')

        # 2. Match rate by inverter with annotations
        inverter_stats = self.analysis_results['inverter_stats']
        axes[0, 1].bar(inverter_stats.index, inverter_stats['Match_Rate'], color='#2196F3')
        axes[0, 1].set_title('Match Rate by Inverter (%)', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('Inverter Name', fontsize=12)
        axes[0, 1].set_ylabel('Match Rate (%)', fontsize=12)
        axes[0, 1].tick_params(axis='x', rotation=45)
        for idx, rate in enumerate(inverter_stats['Match_Rate']):
            axes[0, 1].text(idx, rate + 0.5, f'{rate:.1f}%', ha='center', fontsize=10)

        # 3. Match rate by hour with annotations
        hourly_stats = self.analysis_results['hourly_stats']
        axes[1, 0].bar(hourly_stats.index, hourly_stats['Match_Rate'], color='#9C27B0')
        axes[1, 0].set_title('Match Rate by Hour of Day', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('Hour of Day', fontsize=12)
        axes[1, 0].set_ylabel('Match Rate (%)', fontsize=12)
        for idx, rate in enumerate(hourly_stats['Match_Rate']):
            axes[1, 0].text(idx, rate + 0.5, f'{rate:.1f}%', ha='center', fontsize=10)

        # 4. API vs SCADA scatter plot with regression line
        valid_data = df[df['Match_Status'] != 'Missing Data'].sample(min(1000, len(df)))
        sns.regplot(x='API_Value', y='SCADA_Value', data=valid_data, ax=axes[1, 1], scatter_kws={'alpha':0.6}, line_kws={'color':'red'})
        axes[1, 1].set_xlabel('API Value (KWh)', fontsize=12)
        axes[1, 1].set_ylabel('SCADA Value (KWh)', fontsize=12)
        axes[1, 1].set_title('API vs SCADA Values Correlation', fontsize=14, fontweight='bold')
        axes[1, 1].grid(True)

        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        
        # 1. Match status pie chart
        match_counts = df['Match_Status'].value_counts()
        axes[0, 0].pie(match_counts.values, labels=match_counts.index, autopct='%1.1f%%')
        axes[0, 0].set_title('Overall Match Status Distribution')
        
        # 2. Match rate by inverter
        inverter_stats = self.analysis_results['inverter_stats']
        axes[0, 1].bar(inverter_stats.index, inverter_stats['Match_Rate'])
        axes[0, 1].set_title('Match Rate by Inverter (%)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].set_ylabel('Match Rate (%)')
        
        # 3. Match rate by hour
        hourly_stats = self.analysis_results['hourly_stats']
        axes[1, 0].bar(hourly_stats.index, hourly_stats['Match_Rate'])
        axes[1, 0].set_title('Match Rate by Hour of Day')
        axes[1, 0].set_xlabel('Hour')
        axes[1, 0].set_ylabel('Match Rate (%)')
        
        # 4. API vs SCADA scatter plot
        valid_data = df[df['Match_Status'] != 'Missing Data'].sample(min(1000, len(df)))
        axes[1, 1].scatter(valid_data['API_Value'], valid_data['SCADA_Value'], alpha=0.6)
        max_val = max(valid_data['API_Value'].max(), valid_data['SCADA_Value'].max())
        axes[1, 1].plot([0, max_val], [0, max_val], 'r--', label='Perfect Match')
        axes[1, 1].set_xlabel('API Value (KWh)')
        axes[1, 1].set_ylabel('SCADA Value (KWh)')
        axes[1, 1].set_title('API vs SCADA Values Correlation')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        # Save visualization
        viz_output = os.path.join(self.output_dir, 'validation_charts.png')
        plt.savefig(viz_output, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Visualizations created and saved to: {viz_output}")
    
    def generate_report(self):
        """Generate comprehensive validation report."""
        print(f"\n📋 STEP 6: Generating Report")
        print("-" * 40)
        
        match_rate = self.analysis_results['match_rate']
        total_comparisons = self.analysis_results['total_comparisons']

        # Determine data quality level
        if total_comparisons == 0:
            quality_level = "NO DATA"
            quality_icon = "⚫"
        elif match_rate >= 95:
            quality_level = "EXCELLENT"
            quality_icon = "🟢"
        elif match_rate >= 80:
            quality_level = "GOOD"
            quality_icon = "🟡"
        elif match_rate >= 50:
            quality_level = "MODERATE"
            quality_icon = "🟠"
        else:
            quality_level = "POOR"
            quality_icon = "🔴"
        
        # Generate report
        report_content = f"""
# DATA VALIDATION REPORT
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## EXECUTIVE SUMMARY
{quality_icon} **Data Quality: {quality_level}** (Match Rate: {match_rate:.1f}%)

## KEY METRICS
- Total Comparisons: {self.analysis_results['total_comparisons']:,}
- Matches: {self.analysis_results['match_counts'].get('Match', 0):,}
- Mismatches: {self.analysis_results['match_counts'].get('Mismatch', 0):,}
- Missing Data: {self.analysis_results['match_counts'].get('Missing Data', 0):,}

## MISMATCH ANALYSIS
"""

        if total_comparisons > 0:
            total_mismatches = self.analysis_results['total_mismatches']
            if total_mismatches > 0:
                report_content += f"""- API Higher than SCADA: {self.analysis_results['api_higher']:,} ({self.analysis_results['api_higher']/total_mismatches*100:.1f}%)
- SCADA Higher than API: {self.analysis_results['scada_higher']:,} ({self.analysis_results['scada_higher']/total_mismatches*100:.1f}%)
"""
            else:
                report_content += "- No mismatches found\n"
        else:
            report_content += "- No comparison data available\n"

        report_content += "\n## RECOMMENDATIONS\n"

        if total_comparisons == 0:
            report_content += """
❌ **NO COMPARISON DATA AVAILABLE**
1. Check that API and SCADA data have matching inverter columns
2. Verify column naming conventions (e.g., INV_1, INV_2, etc.)
3. Ensure API data contains individual inverter generation values
4. Review data preprocessing and column mapping
"""
        elif match_rate < 50:
            report_content += """
❌ **CRITICAL ISSUES DETECTED**
1. Investigate measurement units and calculation methods
2. Check for cumulative vs instantaneous value differences
3. Verify timezone and timing synchronization
4. Review data collection procedures
"""
        elif match_rate < 80:
            report_content += """
⚠️ **MODERATE ISSUES DETECTED**
1. Focus on inverters with lowest match rates
2. Check for systematic timing offsets
3. Review measurement precision
"""
        else:
            report_content += """
✅ **GOOD DATA QUALITY**
1. Monitor remaining mismatches for trends
2. Set up automated alerts for deviations
"""
        
        report_content += f"""

## FILES GENERATED
- scada_combined_data.csv - Combined SCADA data
- api_processed_data.csv - Processed API data  
- comparison_results.csv - Detailed comparison results
- analysis_results.txt - Statistical analysis
- validation_charts.png - Visualization charts
- validation_report.md - This report

## INVERTER PERFORMANCE
"""

        if not self.analysis_results['inverter_stats'].empty:
            report_content += f"""Best Match Rate: {self.analysis_results['inverter_stats']['Match_Rate'].max():.1f}%
Worst Match Rate: {self.analysis_results['inverter_stats']['Match_Rate'].min():.1f}%
"""
        else:
            report_content += "No inverter performance data available\n"

        report_content += "\nFor detailed analysis, see the generated CSV files and charts.\n"
        
        # Save report
        report_output = os.path.join(self.output_dir, 'validation_report.md')
        with open(report_output, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📋 Comprehensive report generated: {report_output}")
        print(f"{quality_icon} Overall Data Quality: {quality_level} ({match_rate:.1f}% match rate)")
    
    def run_complete_validation(self):
        """Run the complete validation process."""
        try:
            start_time = datetime.now()
            
            # Execute all steps
            self.load_scada_data()
            self.load_api_data()
            self.align_and_compare_data()
            self.perform_detailed_analysis()
            self.create_visualizations()
            self.generate_report()
            
            # Final summary
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print("\n" + "=" * 60)
            print("🎉 VALIDATION COMPLETE!")
            print("=" * 60)
            print(f"⏱️  Total execution time: {duration:.1f} seconds")
            print(f"📁 All results saved to: {self.output_dir}/")
            print(f"📊 Match rate: {self.analysis_results['match_rate']:.1f}%")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"\n❌ VALIDATION FAILED: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main function to run the complete validation."""
    
    # Configuration
    scada_folders = [r'OneDrive_2025-05-28\Inverter Dump 15 Min\IN.INTE.STOV\Feb-25', r'OneDrive_2025-05-28\Inverter Dump 15 Min\IN.INTE.STOV\Mar-25']
    api_file = 'STOV SOLAR.csv'
    output_dir = 'validation_results'
    
    # Create and run validation system
    validator = DataValidationSystem(
        scada_folders=scada_folders,
        api_file=api_file,
        output_dir=output_dir
    )
    
    success = validator.run_complete_validation()
    
    if success:
        print("\n✅ Run completed successfully!")
        print(f"📁 Check the '{output_dir}' folder for all results.")
    else:
        print("\n❌ Run failed. Check error messages above.")

if __name__ == "__main__":
    main()
